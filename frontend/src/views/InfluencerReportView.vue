<template>
  <div class="influencer-report-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">达人提报管理</h2>
        <p class="page-description">管理达人提报信息，支持状态审核和进度跟踪</p>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="5">
          <a-card class="stat-card">
            <a-statistic title="总提报数" :value="stats.total" />
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card class="stat-card">
            <a-statistic title="审核中" :value="stats.pending" />
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card class="stat-card">
            <a-statistic title="审核通过" :value="stats.approved" />
          </a-card>
        </a-col>
        <a-col :span="5">
          <a-card class="stat-card">
            <a-statistic title="审核拒绝" :value="stats.rejected" />
          </a-card>
        </a-col>
        <a-col :span="4">
          <a-card class="stat-card">
            <a-statistic title="需二次确认" :value="stats.needConfirmation" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="search-card">
      <a-form :model="searchForm" layout="inline" @submit="handleSearch">
        <a-form-item label="达人名称">
          <a-input v-model="searchForm.keyword" placeholder="请输入达人名称" style="width: 160px" allow-clear />
        </a-form-item>
        <a-form-item label="平台">
          <a-select v-model="searchForm.platform" placeholder="请选择平台" style="width: 120px" allow-clear>
            <a-option value="xiaohongshu">小红书</a-option>
            <a-option value="juxingtu">巨量星图</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model="searchForm.status" placeholder="请选择状态" style="width: 140px" allow-clear>
            <a-option value="pending">审核中</a-option>
            <a-option value="approved">审核通过</a-option>
            <a-option value="rejected">审核拒绝</a-option>
            <a-option value="need_confirmation">需二次确认</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="运营负责人">
          <a-input v-model="searchForm.operationManager" placeholder="请输入负责人" style="width: 120px" allow-clear />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="resetSearch">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 提报列表 -->
    <a-card class="table-card">
      <div class="table-action">
        <a-space>
          <!-- 智能提报 -->
          <a-button type="primary" @click="showSmartReportModal">
            <template #icon>
              <icon-plus />
            </template>
            智能提报
          </a-button>
          <!-- 传统新建 -->
          <a-button @click="showCreateModal">
            <template #icon>
              <icon-plus />
            </template>
            手动提报
          </a-button>
          <a-button type="text" @click="refreshData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新数据
          </a-button>
        </a-space>
      </div>
      <a-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        class="layout-auto"
        :border="false"
        :scroll="{ x: 'max-content' }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :row-class="getRowClass"
      >
        <!-- 平台列 -->
        <template #platform="{ record }">
          <a-tag :color="getPlatformColor(record.platform)">
            {{ getPlatformName(record.platform) }}
          </a-tag>
        </template>

        <!-- 链接 -->
        <template #influencerUrl="{ record }">
          <div>
            <a-link v-if="record.influencerUrl" :href="record.influencerUrl" target="_blank">
              {{ record.influencerUrl }}
            </a-link>
            <span v-else>-</span>
          </div>
        </template>

        <!-- 达人名称列 -->
        <template #influencerName="{ record }">
          <div class="influencer-name">
            <span :class="{ 'name-highlight': record.status === 'resubmitting' }">
              {{ record.influencerName }}
            </span>
            <a-tooltip v-if="record.status === 'resubmitting'" content="二次提报">
              <icon-exclamation-circle-fill class="resubmit-icon" />
            </a-tooltip>
          </div>
        </template>

        <!-- 粉丝量列 -->
        <template #followersCount="{ record }">
          {{ formatNumber(record.followersCount) }}
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 提报时间列 -->
        <template #lastSubmittedAt="{ record }">
          {{ formatDate(record.lastSubmittedAt) }}
        </template>

        <!-- 关联作品列 -->
        <template #relatedVideos="{ record }">
          <div class="related-videos-cell">
            <span v-if="!record.relatedVideos || record.relatedVideos.length === 0" class="no-videos">
              无关联作品
            </span>
            <div v-else class="videos-summary">
              <a-tag color="blue">{{ record.relatedVideos.length }} 个作品</a-tag>
              <a-button type="text" size="small" @click="previewRelatedVideos(record)"> 预览 </a-button>
            </div>
          </div>
        </template>

        <!-- 合作对接状态列 -->
        <template #cooperationStatus="{ record }">
          <a-tag v-if="record.cooperationRecordCreated" color="green"> 已创建 </a-tag>
          <a-tag v-else-if="record.status === 'approved'" color="orange"> 待创建 </a-tag>
          <a-tag v-else color="gray"> 不可创建 </a-tag>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <a-space wrap>
            <a-link type="text" size="small" @click="viewDetail(record)"> 查看详情 </a-link>

            <!-- 管理员专属操作 -->
            <template v-if="userStore.isAdmin">
              <a-link
                v-show="record.status !== 'approved'"
                type="text"
                :key="record.id + 'approve'"
                size="small"
                @click="showReviewModal(record, 'approve')"
              >
                审核通过
              </a-link>
              <a-link
                v-show="record.status !== 'rejected'"
                type="text"
                :key="record.id"
                size="small"
                @click="showReviewModal(record, 'reject')"
              >
                审核拒绝
              </a-link>
              <a-link
                v-show="record.status !== 'need_confirmation'"
                type="text"
                :key="record.id + 'need_confirmation'"
                size="small"
                @click="showReviewModal(record, 'need_confirmation')"
              >
                需二次确认
              </a-link>
            </template>

            <!-- 重新提报操作（所有状态都可以重新提报） -->
            <a-link type="text" v-if="record.status === 'rejected'" size="small" @click="showResubmitModal(record)">
              重新提报
            </a-link>

            <!-- 再次提交审核 -->
            <a-link
              v-if="record.status === 'need_confirmation'"
              type="text"
              size="small"
              @click="showResubmitModal(record, 'approve')"
            >
              再次提交审核
            </a-link>

            <!-- 创建合作对接记录 -->
            <a-link
              v-if="record.status === 'approved' && !record.cooperationRecordCreated"
              type="text"
              size="small"
              @click="createCooperationRecord(record)"
            >
              创建合作对接
            </a-link>

            <!-- 通用操作 -->
            <a-link v-if="canEdit(record)" type="text" size="small" @click="editReport(record)"> 编辑 </a-link>
            <a-link v-if="canDelete(record)" type="text" size="small" status="danger" @click="deleteReport(record)">
              删除
            </a-link>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 详情弹窗 -->
    <a-modal v-model:visible="detailModalVisible" title="提报详情" width="800px">
      <div v-if="currentRecord" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="平台">
            <a-tag :color="currentRecord.platform === 'xiaohongshu' ? 'red' : 'blue'">
              {{ currentRecord.platform === 'xiaohongshu' ? '小红书' : '巨量星图' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="达人名称">{{ currentRecord.influencerName }}</a-descriptions-item>
          <a-descriptions-item label="运营负责人">{{ currentRecord.operationManager }}</a-descriptions-item>
          <a-descriptions-item label="粉丝量">{{ formatNumber(currentRecord.followersCount) }}</a-descriptions-item>
          <a-descriptions-item label="播放量中位数">{{ currentRecord.playMid || '-' }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="达人主页链接" :span="2">
            <a-link v-if="currentRecord.influencerUrl" :href="currentRecord.influencerUrl" target="_blank">
              {{ currentRecord.influencerUrl }}
            </a-link>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="选择理由" :span="2">
            <div class="text-content">{{ currentRecord.selectionReason }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="平台达人报价" :span="2">
            <div class="text-content">{{ currentRecord.platformPrice || '-' }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="合作价格" :span="2">
            <div class="text-content">{{ currentRecord.cooperationPrice || '-' }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            <div class="text-content">{{ currentRecord.notes || '-' }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="平台用户ID">{{ currentRecord.platformUserId || '-' }}</a-descriptions-item>
          <a-descriptions-item label="重新提报次数">{{ currentRecord.resubmitCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="审核意见" :span="2" v-if="currentRecord.reviewComment">
            <div class="text-content">{{ currentRecord.reviewComment }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="重新提报说明" :span="2" v-if="currentRecord.resubmitReason">
            <div class="text-content">{{ currentRecord.resubmitReason }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="提报人">{{ currentRecord.submitter?.username }}</a-descriptions-item>
          <a-descriptions-item label="提报时间">{{ formatDate(currentRecord.lastSubmittedAt) }}</a-descriptions-item>
          <a-descriptions-item label="审核人" v-if="currentRecord.reviewer">{{
            currentRecord.reviewer?.username
          }}</a-descriptions-item>
          <a-descriptions-item label="审核时间" v-if="currentRecord.reviewedAt">{{
            formatDate(currentRecord.reviewedAt)
          }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 自定义footer操作按钮组 -->
      <template #footer>
        <div v-if="currentRecord" class="detail-modal-footer">
          <!-- 管理员专属操作按钮 -->
          <div class="admin-actions" v-if="userStore.isAdmin">
            <a-button
              v-if="currentRecord.status !== 'approved'"
              type="primary"
              status="success"
              @click="handleDetailAction('approve')"
            >
              <template #icon>
                <icon-check-circle-fill />
              </template>
              审核通过
            </a-button>

            <a-button
              v-if="currentRecord.status !== 'rejected'"
              type="primary"
              status="danger"
              @click="handleDetailAction('reject')"
            >
              <template #icon>
                <icon-close-circle-fill />
              </template>
              审核拒绝
            </a-button>

            <a-button
              v-if="currentRecord.status !== 'need_confirmation'"
              type="primary"
              status="warning"
              @click="handleDetailAction('need_confirmation')"
            >
              <template #icon>
                <icon-exclamation-circle-fill />
              </template>
              需二次确认
            </a-button>
          </div>

          <!-- 通用操作按钮 -->
          <div class="common-actions">
            <a-button v-if="canEdit(currentRecord)" type="outline" @click="handleDetailAction('edit')"> 编辑 </a-button>

            <a-button v-if="currentRecord.status === 'rejected'" type="outline" @click="handleDetailAction('resubmit')">
              重新提报
            </a-button>

            <a-button
              v-if="canDelete(currentRecord)"
              type="outline"
              status="danger"
              @click="handleDetailAction('delete')"
            >
              删除
            </a-button>
          </div>

          <!-- 关闭按钮 -->
          <div class="close-actions">
            <a-button @click="detailModalVisible = false">关闭</a-button>
          </div>
        </div>
      </template>
    </a-modal>

    <!-- 审核意见弹窗 -->
    <a-modal
      v-model:visible="reviewModalVisible"
      :title="reviewModalTitle"
      width="600px"
      :on-before-ok="handleReviewSubmit"
      @cancel="handleReviewCancel"
      :confirm-loading="reviewSubmitting"
    >
      <a-form :model="reviewForm" layout="vertical" ref="reviewFormRef">
        <a-form-item label="审核意见" field="reviewComment" :rules="[{ required: true, message: '请输入审核意见' }]">
          <a-textarea
            v-model="reviewForm.reviewComment"
            placeholder="请输入审核意见（必填）"
            :rows="4"
            :max-length="500"
            show-word-limit
          />
        </a-form-item>
        <a-alert v-if="reviewForm.action === 'approve'" type="success" message="确认审核通过该提报？">
          <template #icon>
            <icon-check-circle-fill />
          </template>
          确认审核通过该提报？
        </a-alert>
        <a-alert v-if="reviewForm.action === 'reject'" type="error">
          <template #icon>
            <icon-close-circle-fill />
          </template>
          确认审核拒绝该提报？
        </a-alert>
        <a-alert v-if="reviewForm.action === 'need_confirmation'" type="warning">
          <template #icon>
            <icon-exclamation-circle-fill />
          </template>
          确认将该提报标记为需二次确认？
        </a-alert>
      </a-form>
    </a-modal>

    <!-- 重新提报弹窗 -->
    <a-modal
      v-model:visible="resubmitModalVisible"
      title="重新提报"
      width="600px"
      :on-before-ok="handleResubmitSubmit"
      @cancel="handleResubmitCancel"
      :confirm-loading="resubmitSubmitting"
    >
      <a-form :model="resubmitForm" layout="vertical" ref="resubmitFormRef">
        <a-form-item
          label="重新提报说明"
          field="resubmitReason"
          :rules="[{ required: true, message: '请输入重新提报说明' }]"
        >
          <a-textarea
            v-model="resubmitForm.resubmitReason"
            placeholder="请说明重新提报的理由或修改内容（必填）"
            :rows="4"
            :max-length="500"
            show-word-limit
          />
        </a-form-item>
        <a-alert type="info">
          <template #icon>
            <icon-exclamation-circle-fill />
          </template>
          重新提报后，原审核信息将被清空，提报状态变为"审核中"，请谨慎操作。
        </a-alert>
      </a-form>
    </a-modal>

    <!-- 编辑提报表单 -->
    <InfluencerReportForm
      v-model:visible="editFormVisible"
      :is-edit-mode="isEditMode"
      :edit-data="currentEditData"
      @success="handleEditSuccess"
    />

    <!-- 合作对接记录表单 -->
    <CooperationForm
      v-model:visible="cooperationFormVisible"
      :is-edit-mode="false"
      :edit-data="cooperationFormData"
      @success="handleCooperationFormSuccess"
      @cancel="handleCooperationFormCancel"
    />

    <!-- 智能提报模态窗口 -->
    <SmartInfluencerReportModal
      v-model:visible="smartReportModalVisible"
      :influencer-data="smartReportInfluencerData"
      :skip-to-step4="smartReportSkipToStep4"
      @success="handleSmartReportSuccess"
      @cancel="handleSmartReportCancel"
    />

    <!-- 关联作品预览模态窗口 -->
    <a-modal
      v-model:visible="relatedVideosModalVisible"
      title="关联作品预览"
      width="800px"
      :footer="false"
      @cancel="closeRelatedVideosModal"
    >
      <div v-if="currentRelatedVideos && currentRelatedVideos.length > 0" class="related-videos-preview">
        <div class="videos-info">
          <p>共 {{ currentRelatedVideos.length }} 个关联作品</p>
        </div>
        <div class="videos-grid">
          <div v-for="video in currentRelatedVideos" :key="video.videoId" class="video-card" @click="viewVideo(video)">
            <div class="video-card-content">
              <h4 class="video-title">{{ video.title }}</h4>
              <div class="video-stats">
                <span>播放: {{ formatNumber(video.playCount) }}</span>
                <span>点赞: {{ formatNumber(video.likeCount) }}</span>
                <span>收藏: {{ formatNumber(video.collectCount) }}</span>
              </div>
              <div class="video-time">发布时间: {{ formatDate(video.publishTime) }}</div>
            </div>
            <div class="video-actions">
              <a-button
                type="primary"
                size="small"
                @click.stop="viewVideo(video)"
                :loading="loadingNotes[video.videoId]"
              >
                观看
              </a-button>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-videos-preview">
        <a-empty description="暂无关联作品" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import {
  IconSearch,
  IconRefresh,
  IconExclamationCircleFill,
  IconCheckCircleFill,
  IconCloseCircleFill,
  IconPlus
} from '@arco-design/web-vue/es/icon';
import InfluencerReportForm from '@/components/InfluencerReportForm.vue';
import CooperationForm from '@/components/CooperationForm.vue';
import SmartInfluencerReportModal from '@/components/SmartInfluencerReportModal.vue';
import { influencerReportAPI, cooperationAPI, authorVideoAPI } from '@/services/api';
import { useUserStore } from '@/stores/user';
import { useRouter } from 'vue-router';
import { getPlatformName, getPlatformColor, formatNumber, formatPrice, formatDate } from '@/utils/platformUtils';

// 用户状态和路由
const userStore = useUserStore();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const detailModalVisible = ref(false);
const currentRecord = ref(null);

// 审核相关
const reviewModalVisible = ref(false);
const reviewSubmitting = ref(false);
const reviewFormRef = ref(null);
const reviewForm = reactive({
  id: null,
  action: '',
  reviewComment: ''
});

const isEditMode = ref(true);

// 重新提报相关
const resubmitModalVisible = ref(false);
const resubmitSubmitting = ref(false);
const resubmitFormRef = ref(null);
const resubmitForm = reactive({
  id: null,
  resubmitReason: ''
});

// 编辑相关
const editFormVisible = ref(false);
const currentEditData = ref(null);

// 合作记录创建相关
const cooperationCreateModalVisible = ref(false);
const cooperationCreating = ref(false);
const currentReportData = ref(null);

// 合作对接表单相关
const cooperationFormVisible = ref(false);
const cooperationFormData = ref(null);
const cooperationFormMode = ref('create'); // 'create' | 'edit'

// 智能提报相关
const smartReportModalVisible = ref(false);

// 统计数据
const stats = reactive({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0,
  needConfirmation: 0,
  thisMonth: 0,
  thisWeek: 0,
  today: 0
});

// 搜索表单
const searchForm = reactive({
  keyword: '',
  platform: '',
  status: '',
  operationManager: ''
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 计算属性
const reviewModalTitle = computed(() => {
  const actionMap = {
    approve: '审核通过',
    reject: '审核拒绝',
    need_confirmation: '需二次确认'
  };
  return actionMap[reviewForm.action] || '审核';
});

// 表格列配置
const columns = [
  {
    title: '达人名称',
    dataIndex: 'influencerName',
    slotName: 'influencerName',
    fixed: 'left'
  },
  {
    title: '平台',
    dataIndex: 'platform',
    slotName: 'platform'
  },

  {
    title: '运营负责人',
    dataIndex: 'operationManager'
  },
  // 选择理由
  {
    title: '选择理由',
    dataIndex: 'selectionReason',
    width: 300
  },
  // 内容方向
  {
    title: '内容方向',
    dataIndex: 'contentDirection',
    width: 120
  },
  // 合作产品
  {
    title: '合作品',
    dataIndex: 'cooperationProduct',
    width: 120
  },

  {
    title: '粉丝量',
    dataIndex: 'followersCount',
    slotName: 'followersCount'
  },
  {
    title: '播放量中位数',
    dataIndex: 'playMid',
    width: 120
  },
  {
    title: '平台达人报价',
    dataIndex: 'platformPrice',
    width: 200
  },
  {
    title: '合作价格',
    dataIndex: 'cooperationPrice',
    width: 260
  },

  {
    title: '备注',
    dataIndex: 'notes',
    width: 260
  },
  {
    title: '关联作品',
    dataIndex: 'relatedVideos',
    slotName: 'relatedVideos',
    width: 120
  },
  {
    title: '平台用户ID',
    dataIndex: 'platformUserId',
    ellipsis: true,
    tooltip: true,
    width: 120
  },
  {
    title: '审核意见',
    dataIndex: 'reviewComment',
    width: 250
  },
  {
    title: '提报时间',
    dataIndex: 'lastSubmittedAt',
    slotName: 'lastSubmittedAt'
  },
  {
    title: '达人主页链接',
    dataIndex: 'influencerUrl',
    slotName: 'influencerUrl',
    width: 220
  },
  {
    title: '合作对接状态',
    dataIndex: 'cooperationRecordCreated',
    slotName: 'cooperationStatus',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    fixed: 'right'
  },
  {
    title: '操作',
    slotName: 'actions',
    fixed: 'right',
    align: 'left',
    width: 200
  }
];

// 生命周期
onMounted(() => {
  loadData();
});

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...searchForm
    };

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });

    const response = await influencerReportAPI.getList(params);
    if (response.success) {
      tableData.value = response.data;
      pagination.total = response.pagination.total;

      // 获取全部数据用于统计（不分页）
      const statsParams = { ...searchForm };
      Object.keys(statsParams).forEach(key => {
        if (statsParams[key] === '' || statsParams[key] === null || statsParams[key] === undefined) {
          delete statsParams[key];
        }
      });

      const statsResponse = await influencerReportAPI.getList({ ...statsParams, limit: 1000 });
      if (statsResponse.success) {
        updateStats(statsResponse.data);
      }
    } else {
      Message.error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取提报列表失败:', error);
    Message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 更新统计数据
const updateStats = data => {
  stats.total = data.length;
  stats.pending = data.filter(item => item.status === 'pending').length;
  stats.approved = data.filter(item => item.status === 'approved').length;
  stats.rejected = data.filter(item => item.status === 'rejected').length;
  stats.needConfirmation = data.filter(item => item.status === 'need_confirmation').length;

  // 计算时间相关统计
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const thisWeekStart = new Date(today);
  thisWeekStart.setDate(today.getDate() - today.getDay());
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

  stats.today = data.filter(item => {
    const itemDate = new Date(item.lastSubmittedAt);
    return itemDate >= today;
  }).length;

  stats.thisWeek = data.filter(item => {
    const itemDate = new Date(item.lastSubmittedAt);
    return itemDate >= thisWeekStart;
  }).length;

  stats.thisMonth = data.filter(item => {
    const itemDate = new Date(item.lastSubmittedAt);
    return itemDate >= thisMonthStart;
  }).length;
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    platform: '',
    status: '',
    operationManager: ''
  });
  handleSearch();
};

// 刷新数据
const refreshData = () => {
  loadData();
};

const showCreateModal = () => {
  currentEditData.value = {};
  isEditMode.value = false;
  editFormVisible.value = true;
};

// 智能提报相关方法
const showSmartReportModal = () => {
  smartReportModalVisible.value = true;
};

const handleSmartReportSuccess = () => {
  smartReportModalVisible.value = false;
  loadData(); // 刷新列表
};

const handleSmartReportCancel = () => {
  smartReportModalVisible.value = false;
  // 重置智能提报数据
  smartReportInfluencerData.value = null;
  smartReportSkipToStep4.value = false;
};

// 从达人数据创建智能提报（用于从达人页面跳转）
const createSmartReportFromInfluencer = influencerData => {
  smartReportInfluencerData.value = influencerData;
  smartReportSkipToStep4.value = true;
  smartReportModalVisible.value = true;
};

// 智能提报相关数据
const smartReportInfluencerData = ref(null);
const smartReportSkipToStep4 = ref(false);

// 关联作品预览相关
const relatedVideosModalVisible = ref(false);
const currentRelatedVideos = ref([]);
const loadingNotes = ref({}); // 用于跟踪每个帖子的加载状态

// 预览关联作品
const previewRelatedVideos = record => {
  if (record.relatedVideos && record.relatedVideos.length > 0) {
    currentRelatedVideos.value = record.relatedVideos;
    relatedVideosModalVisible.value = true;
  } else {
    Message.info('该提报暂无关联作品');
  }
};

// 关闭关联作品预览
const closeRelatedVideosModal = () => {
  relatedVideosModalVisible.value = false;
  currentRelatedVideos.value = [];
  loadingNotes.value = {}; // 清除加载状态
};

// 观看作品
const viewVideo = async video => {
  // 智能判断平台类型
  // 如果链接中包含xiaohongshu，则认为是小红书
  const platform = video.videoUrl.includes('xiaohongshu') ? 'xiaohongshu' : 'other';

  if (platform === 'xiaohongshu' && video.videoId) {
    // 小红书平台：调用API获取最新详情
    await handleXiaohongshuWatch(video);
  } else if (video.videoUrl) {
    // 其他平台或有直接链接：直接打开链接
    window.open(video.videoUrl, '_blank');
  } else {
    Message.warning('该作品暂无观看链接');
  }
};

/**
 * 处理小红书帖子观看按钮点击
 * 调用API获取最新帖子详情并打开链接
 */
const handleXiaohongshuWatch = async video => {
  try {
    // 设置加载状态
    loadingNotes.value[video.videoId] = true;

    // 调用API获取帖子详情
    const response = await authorVideoAPI.getXiaohongshuNoteDetail(video.videoId);

    if (response.success && response.data) {
      // 打开帖子链接
      window.open(response.data.noteLink, '_blank');

      // 显示成功消息
      Message.success('已打开最新帖子链接');
    } else {
      // 显示错误消息
      Message.error(response.message || '获取帖子详情失败');
    }
  } catch (error) {
    console.error('获取小红书帖子详情失败:', error);
    Message.error('获取帖子详情失败，请稍后重试');
  } finally {
    // 清除加载状态
    loadingNotes.value[video.videoId] = false;
  }
};

// 复制作品链接
const copyVideoUrl = async video => {
  if (!video.videoUrl) {
    Message.warning('该作品暂无链接可复制');
    return;
  }

  try {
    await navigator.clipboard.writeText(video.videoUrl);
    Message.success('链接已复制到剪贴板');
  } catch (error) {
    console.error('复制链接失败:', error);

    // 降级方案
    try {
      const textArea = document.createElement('textarea');
      textArea.value = video.videoUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      Message.success('链接已复制到剪贴板');
    } catch (fallbackError) {
      Message.error('复制链接失败，请手动复制');
    }
  }
};

// 分页变化
const handlePageChange = page => {
  pagination.current = page;
  loadData();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  loadData();
};

// 查看详情
const viewDetail = record => {
  currentRecord.value = record;
  detailModalVisible.value = true;
};

// 处理详情弹窗操作
const handleDetailAction = action => {
  if (!currentRecord.value) return;

  // 关闭详情弹窗
  detailModalVisible.value = false;

  // 根据操作类型执行相应的方法
  switch (action) {
    case 'approve':
      showReviewModal(currentRecord.value, 'approve');
      break;
    case 'reject':
      showReviewModal(currentRecord.value, 'reject');
      break;
    case 'need_confirmation':
      showReviewModal(currentRecord.value, 'need_confirmation');
      break;
    case 'edit':
      editReport(currentRecord.value);
      break;
    case 'resubmit':
      showResubmitModal(currentRecord.value);
      break;
    case 'delete':
      deleteReport(currentRecord.value);
      break;
    default:
      console.warn('未知的操作类型:', action);
  }
};

// 更新状态
const updateStatus = async (record, status) => {
  try {
    const response = await influencerReportAPI.updateStatus(record.id, status);
    if (response.success) {
      Message.success('状态更新成功');
      loadData();
    } else {
      Message.error(response.message || '状态更新失败');
    }
  } catch (error) {
    console.error('更新状态失败:', error);
    Message.error('状态更新失败');
  }
};

// 编辑提报
const editReport = record => {
  currentEditData.value = { ...record };
  isEditMode.value = true;
  editFormVisible.value = true;
};

// 删除提报
const deleteReport = record => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除达人"${record.influencerName}"的提报吗？`,
    onOk: async () => {
      try {
        const response = await influencerReportAPI.delete(record.id);
        if (response.success) {
          Message.success('删除成功');
          loadData();
        } else {
          Message.error(response.message || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        Message.error('删除失败');
      }
    }
  });
};

// 权限检查
const canEdit = record => {
  return userStore.isAdmin || record.submittedBy === userStore.user?.id;
};

const canDelete = record => {
  return userStore.isAdmin || record.submittedBy === userStore.user?.id;
};

// 显示审核弹窗
const showReviewModal = (record, action) => {
  reviewForm.id = record.id;
  reviewForm.action = action;
  reviewForm.reviewComment = '';
  reviewModalVisible.value = true;
};

// 处理审核提交
const handleReviewSubmit = async () => {
  try {
    const valid = await reviewFormRef.value?.validate();
    if (valid) return false;

    reviewSubmitting.value = true;

    let response;
    const { id, action, reviewComment } = reviewForm;

    switch (action) {
      case 'approve':
        response = await influencerReportAPI.approve(id, reviewComment);
        break;
      case 'reject':
        response = await influencerReportAPI.reject(id, reviewComment);
        break;
      case 'need_confirmation':
        response = await influencerReportAPI.needConfirmation(id, reviewComment);
        break;
      default:
        throw new Error('未知的审核操作');
    }

    if (response.success) {
      Message.success(response.message || '审核成功');
      reviewModalVisible.value = false;

      // 如果是审核通过且可以创建合作记录，显示确认弹窗
      if (action === 'approve' && response.extra?.canCreateCooperation) {
        showCooperationCreateConfirm(response.extra.reportData);
      }

      loadData(); // 刷新列表
    } else {
      Message.error(response.message || '审核失败');
    }
  } catch (error) {
    console.error('审核失败:', error);
    Message.error('审核失败');
  }
};

// 取消审核
const handleReviewCancel = () => {
  reviewModalVisible.value = false;
  reviewForm.id = null;
  reviewForm.action = '';
  reviewForm.reviewComment = '';
};

// 显示重新提报弹窗
const showResubmitModal = record => {
  resubmitForm.id = record.id;
  resubmitForm.resubmitReason = '';
  resubmitModalVisible.value = true;
};

// 处理重新提报提交
const handleResubmitSubmit = async () => {
  try {
    const valid = await resubmitFormRef.value?.validate();
    if (valid) return false;

    resubmitSubmitting.value = true;

    const { id, resubmitReason } = resubmitForm;
    const response = await influencerReportAPI.resubmit(id, resubmitReason);

    if (response.success) {
      Message.success(response.message || '重新提报成功');
      resubmitModalVisible.value = false;
      loadData(); // 刷新列表
    } else {
      Message.error(response.message || '重新提报失败');
    }
  } catch (error) {
    console.error('重新提报失败:', error);
    Message.error('重新提报失败');
  }
};

// 取消重新提报
const handleResubmitCancel = () => {
  resubmitModalVisible.value = false;
  resubmitForm.id = null;
  resubmitForm.resubmitReason = '';
};

// 编辑成功处理
const handleEditSuccess = () => {
  editFormVisible.value = false;
  currentEditData.value = null;
  loadData(); // 刷新列表数据和统计信息
};

const getStatusColor = status => {
  const colors = {
    pending: 'blue',
    approved: 'green',
    rejected: 'red',
    need_confirmation: 'orange'
  };
  return colors[status] || 'gray';
};

const getStatusText = status => {
  const texts = {
    pending: '审核中',
    approved: '审核通过',
    rejected: '审核拒绝',
    need_confirmation: '需二次确认'
  };
  return texts[status] || status;
};

const getRowClass = record => {
  if (record.status === 'need_confirmation') {
    return 'row-need-confirmation';
  }
  if (record.status === 'approved') {
    return 'row-approved';
  }
  if (record.status === 'rejected') {
    return 'row-rejected';
  }
  return '';
};

// 合作记录创建相关方法
const showCooperationCreateConfirm = reportData => {
  currentReportData.value = reportData;

  Modal.confirm({
    title: '创建合作记录',
    content: `达人"${reportData.influencerName}"的提报已审核通过，是否要基于此提报信息自动创建合作对接记录？`,
    okText: '创建',
    cancelText: '暂不创建',
    onOk: () => {
      createCooperationFromReport(reportData);
    }
  });
};

// 创建合作对接记录 - 打开表单弹窗
const createCooperationRecord = reportData => {
  // 生成预填充的合作对接数据
  const prefilledData = generateCooperationDataFromReport(reportData);

  // 设置表单数据和模式
  cooperationFormData.value = prefilledData;
  cooperationFormMode.value = 'create';

  // 保存当前提报数据，用于后续关联
  currentReportData.value = reportData;

  // 显示表单弹窗
  cooperationFormVisible.value = true;
};

// 根据达人提报数据生成合作对接记录的预填充数据
const generateCooperationDataFromReport = reportData => {
  const currentDate = new Date().toISOString().split('T')[0];

  // 平台映射
  const platformMap = {
    xiaohongshu: '小红书',
    douyin: '抖音'
  };

  return {
    // 客户信息模块
    customerName: reportData.influencerName || '',
    customerHomepage: reportData.influencerUrl || '',
    seedingPlatform: platformMap[reportData.platform] || reportData.platform,
    bloggerFansCount: reportData.followersCount?.toString() || '',
    influencerPlatformId: reportData.platformUserId || '',
    bloggerWechatAndNotes: `来源：达人提报\n选择理由：${reportData.selectionReason || ''}\n平台报价：${
      reportData.platformPrice || '未提供'
    }\n备注：${reportData.notes || ''}`,
    receiverAddress: reportData.receiverAddress || '',

    // 协议信息模块 - 合作前信息
    title: `${platformMap[reportData.platform] || reportData.platform}-${reportData.influencerName}-${currentDate}`,
    publishPlatform: platformMap[reportData.platform] || reportData.platform,
    cooperationAmount: reportData.cooperationPrice || '',
    cooperationNotes: `达人提报ID：${reportData.id}\n运营负责人：${reportData.operationManager || ''}\n提报时间：${
      reportData.lastSubmittedAt ? formatDate(reportData.lastSubmittedAt) : ''
    }`,

    // 关联字段
    influencerReportId: reportData.id,

    // 保留原有字段（向后兼容）
    cooperationMonth: new Date().toISOString().slice(0, 7),
    platform: reportData.platform,
    bloggerName: reportData.influencerName,
    responsiblePerson: reportData.operationManager,
    influencerHomepage: reportData.influencerUrl,
    cooperationPrice: reportData.cooperationPrice || ''
  };
};

const createCooperationFromReport = async reportData => {
  // 保留原有方法以兼容现有代码
  await createCooperationRecord(reportData);
};

// 处理合作对接表单提交成功
const handleCooperationFormSuccess = async cooperationRecord => {
  try {
    // 关闭表单弹窗
    cooperationFormVisible.value = false;

    // 如果有关联的提报记录，更新其状态
    if (currentReportData.value && cooperationRecord.id) {
      try {
        // 更新提报记录的关联状态
        const reportId = currentReportData.value.id;
        await updateReportCooperationStatus(reportId, cooperationRecord.id);

        Message.success('合作对接记录创建成功，提报状态已更新！');
      } catch (updateError) {
        console.error('更新提报关联状态失败:', updateError);
        Message.warning('合作对接记录创建成功，但提报状态更新失败');
      }
    } else {
      Message.success('合作对接记录创建成功！');
    }

    // 刷新列表数据
    await loadData();

    // 询问是否跳转到合作记录详情页面
    Modal.confirm({
      title: '创建成功',
      content: '合作对接记录已创建成功，是否要跳转到合作对接管理页面查看？',
      okText: '跳转',
      cancelText: '留在当前页面',
      onOk: () => {
        router.push('/cooperation');
      }
    });
  } catch (error) {
    console.error('处理合作对接表单成功回调失败:', error);
    Message.error('操作完成，但部分后续处理失败');
  } finally {
    // 清理数据
    currentReportData.value = null;
    cooperationFormData.value = null;
  }
};

// 更新提报记录的合作对接关联状态
const updateReportCooperationStatus = async (reportId, cooperationId) => {
  // 这里可以调用后端API更新提报记录的关联状态
  // 暂时使用前端状态更新，实际应该调用API
  const reportIndex = tableData.value.findIndex(item => item.id === reportId);
  if (reportIndex !== -1) {
    tableData.value[reportIndex].cooperationRecordId = cooperationId;
    tableData.value[reportIndex].cooperationRecordCreated = true;
  }
};

// 处理合作对接表单取消
const handleCooperationFormCancel = () => {
  cooperationFormVisible.value = false;
  currentReportData.value = null;
  cooperationFormData.value = null;
};
</script>

<style scoped>
.influencer-report-view {
  padding: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.page-description {
  margin: 4px 0 0 0;
  color: #86909c;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.search-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 16px;
}

.table-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.influencer-name {
  display: flex;
  align-items: center;
  gap: 4px;
}

.name-highlight {
  color: #f53f3f;
  font-weight: 500;
}

.resubmit-icon {
  color: #f53f3f;
  font-size: 14px;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.text-content {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 100px;
  overflow-y: auto;
}

/* 详情弹窗footer样式 */
.detail-modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.admin-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.common-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.close-actions {
  margin-left: auto;
}

/* 响应式设计 - 小屏幕下按钮垂直排列 */
@media (max-width: 768px) {
  .detail-modal-footer {
    flex-direction: column;
    align-items: stretch;
  }

  .admin-actions,
  .common-actions {
    justify-content: center;
  }

  .close-actions {
    margin-left: 0;
    display: flex;
    justify-content: center;
  }
}

/* 行背景色 */
:deep(.row-need-confirmation) {
  background-color: #fffbf0 !important;
}

:deep(.row-approved) {
  background-color: #f6ffed !important;
}

:deep(.row-rejected) {
  background-color: #fff2f0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards .arco-col {
    margin-bottom: 8px;
  }

  .search-card .arco-form {
    flex-direction: column;
  }

  .search-card .arco-form-item {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .table-action {
    flex-direction: column;
    gap: 8px;
  }
}

/* 关联作品相关样式 */
.related-videos-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-videos {
  color: #86909c;
  font-size: 12px;
}

.videos-summary {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 关联作品预览样式 */
.related-videos-preview {
  padding: 16px 0;
}

.videos-info {
  margin-bottom: 16px;
  color: #4e5969;
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.video-card {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 12px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.video-card:hover {
  border-color: #165dff;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.video-card-content {
  margin-bottom: 12px;
}

.video-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.video-stats {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #86909c;
}

.video-time {
  font-size: 12px;
  color: #86909c;
}

.video-actions {
  display: flex;
  gap: 8px;
}

.no-videos-preview {
  text-align: center;
  padding: 40px 20px;
}
</style>
