const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const DateFormatter = require('../utils/dateFormatter');

/**
 * 达人提报模型
 * 存储达人提报信息，包含提报状态、保护期管理等功能
 */
const InfluencerReport = sequelize.define(
  'InfluencerReport',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    platform: {
      type: DataTypes.ENUM(
        'xiaohongshu',
        'juxingtu',
        '淘宝',
        '快手',
        'B站',
        '微信&视频号',
        '微博',
        '知乎',
        '其他',
        '置换'
      ),
      allowNull: false,
      comment: '平台类型'
    },
    influencerName: {
      type: DataTypes.STRING(100),
      field: 'influencer_name',
      allowNull: false,
      comment: '达人名称'
    },
    operationManager: {
      type: DataTypes.STRING(100),
      field: 'operation_manager',
      allowNull: false,
      comment: '运营负责人'
    },
    influencerUrl: {
      type: DataTypes.STRING(500),
      field: 'influencer_url',
      comment: '达人主页链接'
    },
    followersCount: {
      type: DataTypes.INTEGER,
      field: 'followers_count',
      defaultValue: 0,
      comment: '粉丝数量'
    },
    playMid: {
      type: DataTypes.STRING(50),
      field: 'play_mid',
      comment: '小红书/星图播放量中位数'
    },
    selectionReason: {
      type: DataTypes.TEXT,
      field: 'selection_reason',
      allowNull: false,
      comment: '选择理由'
    },
    platformPrice: {
      type: DataTypes.TEXT,
      field: 'platform_price',
      comment: '平台达人报价'
    },
    cooperationPrice: {
      type: DataTypes.TEXT,
      field: 'cooperation_price',
      comment: '合作价格'
    },
    notes: {
      type: DataTypes.TEXT,
      comment: '备注'
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'need_confirmation'),
      defaultValue: 'pending',
      comment: '审核状态：审核中/审核通过/审核拒绝/需二次确认'
    },
    submittedBy: {
      type: DataTypes.INTEGER,
      field: 'submitted_by',
      allowNull: false,
      comment: '提报人ID'
    },
    platformUserId: {
      type: DataTypes.STRING(100),
      field: 'platform_user_id',
      comment: '平台用户ID（统一标识达人）'
    },
    relatedVideos: {
      type: DataTypes.JSON,
      field: 'related_videos',
      comment: '关联的作品详情数据（JSON格式）'
    },
    lastSubmittedAt: {
      type: DataTypes.DATE,
      field: 'last_submitted_at',
      defaultValue: DataTypes.NOW,
      comment: '最后提报时间（用于30天保护期计算）'
    },
    // 审核流程相关字段
    reviewComment: {
      type: DataTypes.TEXT,
      field: 'review_comment',
      comment: '审核意见'
    },
    reviewedBy: {
      type: DataTypes.INTEGER,
      field: 'reviewed_by',
      comment: '审核人员ID'
    },
    reviewedAt: {
      type: DataTypes.DATE,
      field: 'reviewed_at',
      comment: '审核时间'
    },
    resubmitReason: {
      type: DataTypes.TEXT,
      field: 'resubmit_reason',
      comment: '重新提报说明/理由'
    },
    resubmitCount: {
      type: DataTypes.INTEGER,
      field: 'resubmit_count',
      defaultValue: 0,
      comment: '重新提报次数'
    },
    // 合作对接记录关联字段
    cooperationRecordId: {
      type: DataTypes.INTEGER,
      field: 'cooperation_record_id',
      allowNull: true,
      comment: '关联的合作对接记录ID'
    },
    cooperationRecordCreated: {
      type: DataTypes.BOOLEAN,
      field: 'cooperation_record_created',
      defaultValue: false,
      comment: '是否已创建合作对接记录'
    }
  },
  {
    tableName: 'influencer_reports',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['platform']
      },
      {
        fields: ['influencer_name']
      },
      {
        fields: ['status']
      },
      {
        fields: ['submitted_by']
      },
      {
        fields: ['reviewed_by']
      },
      {
        fields: ['last_submitted_at']
      },
      {
        fields: ['reviewed_at']
      },
      {
        // 复合索引用于30天保护期查询
        name: 'idx_protection_check',
        fields: ['influencer_name', 'platform', 'status', 'last_submitted_at']
      },
      {
        // 复合索引用于审核查询
        name: 'idx_review_query',
        fields: ['status', 'reviewed_by', 'reviewed_at']
      }
    ]
  }
);

// 重写toJSON方法，自动格式化时间字段
InfluencerReport.prototype.toJSON = function () {
  const values = Object.assign({}, this.get());
  return DateFormatter.formatObject(values);
};

module.exports = InfluencerReport;
