/**
 * 达人提报控制器
 *
 * 功能说明：
 * - 提供达人提报的完整CRUD操作
 * - 实现30天保护期逻辑
 * - 支持权限控制（普通用户创建，管理员审核）
 * - 支持从我的达人和达人公海快捷提报
 *
 * 接口列表：
 * - GET /api/influencer-reports - 获取提报列表
 * - POST /api/influencer-reports - 创建新提报
 * - PUT /api/influencer-reports/:id - 更新提报信息
 * - PUT /api/influencer-reports/:id/status - 更新提报状态（管理员）
 * - DELETE /api/influencer-reports/:id - 删除提报
 * - POST /api/influencer-reports/check-protection - 检查30天保护期
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const { InfluencerReport, User, MyInfluencer, PublicInfluencer, CooperationManagement } = require('../models');
const ResponseUtil = require('../utils/response');
const ValidationUtil = require('../utils/validation');
const { Op } = require('sequelize');
const cooperationService = require('../services/CooperationService');
const CrmIntegrationService = require('../services/CrmIntegrationService');

class InfluencerReportController {
  /**
   * 获取达人提报列表
   * 支持分页、搜索、筛选、排序
   */
  static async getReports(ctx) {
    try {
      const {
        page = 1,
        limit = 10,
        keyword,
        platform,
        status,
        operationManager,
        sortBy = 'lastSubmittedAt',
        sortOrder = 'DESC'
      } = ctx.query;

      // 构建查询条件
      const where = {};

      if (keyword) {
        where.influencerName = {
          [Op.like]: `%${keyword}%`
        };
      }

      if (platform) {
        where.platform = platform;
      }

      if (status) {
        where.status = status;
      }

      if (operationManager) {
        where.operationManager = {
          [Op.like]: `%${operationManager}%`
        };
      }

      // 分页参数
      const offset = (parseInt(page) - 1) * parseInt(limit);
      const pageLimit = parseInt(limit);

      // 查询数据
      const { count, rows } = await InfluencerReport.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username', 'email'],
            required: false
          }
        ],
        order: [[sortBy, sortOrder.toUpperCase()]],
        limit: pageLimit,
        offset,
        distinct: true
      });

      ResponseUtil.successWithPagination(
        ctx,
        rows,
        {
          page: parseInt(page),
          limit: pageLimit,
          total: count,
          pages: Math.ceil(count / pageLimit)
        },
        '获取提报列表成功'
      );
    } catch (error) {
      console.error('获取提报列表失败:', error);
      ResponseUtil.error(ctx, '获取提报列表失败', 500);
    }
  }

  /**
   * 检查30天保护期
   * 用于前端提报前的验证
   */
  static async checkProtectionPeriod(ctx) {
    try {
      const { influencerName, platform } = ctx.request.body;

      if (!influencerName || !platform) {
        return ResponseUtil.validationError(ctx, '达人名称和平台不能为空');
      }

      // 查找最近的提报记录
      const existingReport = await InfluencerReport.findOne({
        where: {
          influencerName,
          platform,
          status: {
            [Op.in]: ['submitting', 'need_communication']
          }
        },
        order: [['lastSubmittedAt', 'DESC']]
      });

      if (!existingReport) {
        return ResponseUtil.success(ctx, {
          canSubmit: true,
          isFirstSubmit: true,
          message: '可以提报'
        });
      }

      // 计算距离上次提报的天数
      const now = new Date();
      const lastSubmitted = new Date(existingReport.lastSubmittedAt);
      const daysDiff = Math.floor((now - lastSubmitted) / (1000 * 60 * 60 * 24));

      if (daysDiff < 30) {
        return ResponseUtil.success(ctx, {
          canSubmit: false,
          isFirstSubmit: false,
          daysDiff,
          remainingDays: 30 - daysDiff,
          message: `提报保护30天内，无法再次提报，需要等过保护期（还需等待${30 - daysDiff}天）`
        });
      } else {
        return ResponseUtil.success(ctx, {
          canSubmit: true,
          isFirstSubmit: false,
          isResubmit: true,
          daysDiff,
          message: '超过保护期，可以二次提报'
        });
      }
    } catch (error) {
      console.error('检查保护期失败:', error);
      ResponseUtil.error(ctx, '检查保护期失败', 500);
    }
  }

  /**
   * 检查重复提报控制API
   */
  static async checkDuplicateReportAPI(ctx) {
    try {
      const { influencerName, platform, operationManager } = ctx.request.body;

      // 验证必填字段
      if (!influencerName || !platform || !operationManager) {
        return ResponseUtil.validationError(ctx, '达人名称、平台和运营负责人不能为空');
      }

      // 验证平台类型
      const { PLATFORM_CONSTANTS } = require('../config/constants');
      if (!PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform)) {
        return ResponseUtil.error(ctx, '平台类型无效', 400);
      }

      // 1. 先进行现有的平台校验
      const platformResult = await InfluencerReportController.checkDuplicateReport(
        influencerName,
        platform,
        operationManager
      );

      // 如果平台校验不通过，直接返回
      if (!platformResult.canSubmit) {
        return ResponseUtil.success(ctx, platformResult, '检查完成');
      }

      // 2. 进行CRM系统校验
      try {
        const crmResult = await InfluencerReportController.checkCrmDuplicateReport(influencerName, ctx.state.user);

        // 如果CRM校验不通过，返回CRM校验结果
        if (!crmResult.canSubmit) {
          return ResponseUtil.success(ctx, crmResult, '检查完成');
        }

        // 3. 两个校验都通过，返回成功结果
        return ResponseUtil.success(
          ctx,
          {
            canSubmit: true,
            message: '平台和CRM校验均通过，允许提报',
            platformCheck: platformResult,
            crmCheck: crmResult
          },
          '检查完成'
        );
      } catch (crmError) {
        console.warn('CRM校验失败，仅使用平台校验结果:', crmError.message);
        // CRM校验失败时，仍然返回平台校验结果，但添加警告信息
        return ResponseUtil.success(
          ctx,
          {
            ...platformResult,
            crmWarning: `CRM校验失败: ${crmError.message}`
          },
          '检查完成（CRM校验失败）'
        );
      }
    } catch (error) {
      console.error('检查重复提报失败:', error);
      ResponseUtil.error(ctx, '检查重复提报失败', 500);
    }
  }

  /**
   * 检查Cookie状态（用于智能提报）
   */
  static async checkCookieStatus(ctx) {
    try {
      const { platform } = ctx.request.body;

      // 验证平台类型
      if (!platform || !['xiaohongshu', 'juxingtu'].includes(platform)) {
        return ResponseUtil.error(ctx, '请指定有效的平台类型', 400);
      }

      if (platform === 'xiaohongshu') {
        const CookieManager = require('../services/CookieManager');
        const cookieManager = new CookieManager();

        const availableCookie = await cookieManager.getAvailableCookie('xiaohongshu');

        if (availableCookie) {
          return ResponseUtil.success(
            ctx,
            {
              hasAvailableCookie: true,
              accountName: availableCookie.accountName,
              message: 'Cookie状态正常，可以使用智能提报功能'
            },
            'Cookie状态检查完成'
          );
        } else {
          return ResponseUtil.success(
            ctx,
            {
              hasAvailableCookie: false,
              message: '没有可用的小红书Cookie，请联系管理员添加Cookie配置'
            },
            'Cookie状态检查完成'
          );
        }
      } else {
        // 巨量星图暂时不需要Cookie
        return ResponseUtil.success(
          ctx,
          {
            hasAvailableCookie: true,
            message: '巨量星图平台暂不需要Cookie配置'
          },
          'Cookie状态检查完成'
        );
      }
    } catch (error) {
      console.error('检查Cookie状态失败:', error);
      return ResponseUtil.error(ctx, error.message || '检查Cookie状态失败', 500);
    }
  }

  /**
   * 获取达人基础信息（用于智能提报）
   */
  static async getAuthorInfo(ctx) {
    try {
      const { platform, authorId } = ctx.request.body;

      // 验证必填字段
      if (!platform || !authorId) {
        return ResponseUtil.error(ctx, '平台和达人ID不能为空', 400);
      }

      // 验证平台类型
      const { PLATFORM_CONSTANTS } = require('../config/constants');
      if (!PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform)) {
        return ResponseUtil.error(ctx, '不支持的平台类型', 400);
      }

      // 检查是否为支持爬虫的平台
      if (!PLATFORM_CONSTANTS.CRAWLER_SUPPORTED.includes(platform)) {
        return ResponseUtil.error(ctx, '该平台暂不支持智能提报功能，请使用传统提报方式', 400);
      }

      // 根据平台调用相应的爬虫服务
      let authorInfo = null;

      if (platform === 'xiaohongshu') {
        const XiaohongshuCrawler = require('../services/crawler/crawlers/XiaohongshuCrawler');
        const crawler = new XiaohongshuCrawler();

        // 先刷新Cookie
        try {
          await crawler.refreshCookie();
        } catch (cookieError) {
          console.warn('获取Cookie失败，尝试使用默认配置:', cookieError.message);
          // 继续执行，让爬虫自己处理Cookie错误
        }

        // 获取达人页面信息
        const cardInfo = await crawler.getAuthorCardInfo(authorId);

        console.log('📊 获取到的达人页面信息:', JSON.stringify(cardInfo, null, 2));

        // 转换为统一格式
        authorInfo = {
          avatarUrl: cardInfo.headPhoto,
          influencerName: cardInfo.name || '未知达人',
          platformUserId: authorId,
          platform: platform,
          followersCount: cardInfo.fansCount || 0,
          playMid: cardInfo.clickMidNum || 0,
          priceInfo: {
            post: cardInfo.picturePrice,
            video: cardInfo.videoPrice,
            live: cardInfo.lowerPrice
          },
          influencerUrl: `https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/${authorId}?source=Advertiser_Kol`
        };
      } else if (platform === 'juxingtu') {
        // 巨量星图平台 - 调用专用的达人详情获取方法
        try {
          // 调用专用的达人详情获取方法
          const result = await InfluencerReportController.getAuthorDetailInternal(platform, authorId);

          // 转换为统一格式
          authorInfo = {
            avatarUrl: result.avatarUrl,
            influencerName: result.nickname || '未知达人',
            platformUserId: authorId,
            platform: platform,
            followersCount: result.followersCount || 0,
            playMid: result.playMid || 0,
            videoCount: result.videoStats?.videoCount || 0,
            averagePlay: result.videoStats?.averagePlay || 0,
            city: result.city || '未知',
            uniqueId: result.uniqueId || '未知',
            contactInfo: result.contactInfo || {},
            priceInfo: null, // 巨量星图暂不提供报价信息
            videoStats: result.videoStats || {},
            authorExtInfo: result.authorExtInfo || null,
            influencerUrl: `https://www.xingtu.cn/ad/creator/author/douyin/${authorId}`,
            rawData: result.rawData || null
          };
        } catch (error) {
          console.error(`❌ 获取巨量星图达人 ${authorId} 详细信息失败:`, error.message);
          throw error;
        }
      }

      if (!authorInfo) {
        console.error('❌ authorInfo 为空，这不应该发生');
        return ResponseUtil.error(ctx, '获取达人信息失败，请检查达人ID是否正确', 404);
      }

      console.log('✅ 成功获取达人信息:', JSON.stringify(authorInfo, null, 2));

      return ResponseUtil.success(ctx, authorInfo, '获取达人信息成功');
    } catch (error) {
      console.error('获取达人信息失败:', error);

      // 特殊处理Cookie相关错误
      if (error.message.includes('没有可用的Cookie')) {
        return ResponseUtil.error(ctx, '系统暂时无法获取达人信息，请稍后重试或联系管理员检查Cookie配置', 503);
      }

      return ResponseUtil.error(ctx, error.message || '获取达人信息失败', 500);
    }
  }

  /**
   * 内部方法：获取达人详细信息
   * @param {string} platform 平台类型
   * @param {string} authorId 达人ID
   * @returns {Object} 达人详细信息
   */
  static async getAuthorDetailInternal(platform, authorId) {
    if (platform === 'juxingtu') {
      const XingtuCrawler = require('../services/crawler/crawlers/XingtuCrawler');
      const crawler = new XingtuCrawler();

      // 先刷新Cookie
      try {
        await crawler.refreshCookie();
      } catch (cookieError) {
        console.warn('获取巨量星图Cookie失败:', cookieError.message);
        throw new Error('没有可用的巨量星图Cookie，请先添加有效的Cookie后再执行查询');
      }

      // 获取达人详细信息
      const authorDetail = await crawler.getAuthorDetail(authorId);

      if (!authorDetail) {
        console.error(`❌ 获取巨量星图达人 ${authorId} 详细信息失败`);
        throw new Error('获取达人详细信息失败，请检查达人ID是否正确');
      }

      console.log('📊 获取到的巨量星图达人详细信息:', JSON.stringify(authorDetail, null, 2));
      return authorDetail;
    } else if (platform === 'xiaohongshu') {
      const XiaohongshuCrawler = require('../services/crawler/crawlers/XiaohongshuCrawler');
      const crawler = new XiaohongshuCrawler();

      // 先刷新Cookie
      try {
        await crawler.refreshCookie();
      } catch (cookieError) {
        console.warn('获取小红书Cookie失败，尝试使用默认配置:', cookieError.message);
        // 继续执行，让爬虫自己处理Cookie错误
      }

      // 获取达人页面信息
      const cardInfo = await crawler.getAuthorCardInfo(authorId);

      console.log('📊 获取到的小红书达人详细信息:', JSON.stringify(cardInfo, null, 2));

      // 构建详细信息对象
      return {
        platform: 'xiaohongshu',
        platformUserId: authorId,
        nickname: cardInfo.name || '未知达人',
        avatarUrl: cardInfo.headPhoto,
        followersCount: cardInfo.fansCount || 0,
        playMid: cardInfo.clickMidNum || 0,
        priceInfo: {
          post: cardInfo.picturePrice,
          video: cardInfo.videoPrice,
          live: cardInfo.lowerPrice
        },
        influencerUrl: `https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/${authorId}?source=Advertiser_Kol`,
        rawData: cardInfo,
        authorExtInfo: null // 小红书暂不支持扩展信息
      };
    } else {
      throw new Error('不支持的平台类型');
    }
  }

  /**
   * 获取达人详细信息（专用方法）
   * 支持巨量星图和小红书平台的达人详情获取
   */
  static async getAuthorDetail(ctx) {
    try {
      const { platform, authorId } = ctx.request.body;

      // 验证必填字段
      if (!platform || !authorId) {
        return ResponseUtil.error(ctx, '平台和达人ID不能为空', 400);
      }

      // 验证平台类型
      if (!['xiaohongshu', 'juxingtu'].includes(platform)) {
        return ResponseUtil.error(ctx, '不支持的平台类型', 400);
      }

      let authorDetail = null;

      if (platform === 'xiaohongshu') {
        const XiaohongshuCrawler = require('../services/crawler/crawlers/XiaohongshuCrawler');
        const crawler = new XiaohongshuCrawler();

        // 先刷新Cookie
        try {
          await crawler.refreshCookie();
        } catch (cookieError) {
          console.warn('获取小红书Cookie失败，尝试使用默认配置:', cookieError.message);
          // 继续执行，让爬虫自己处理Cookie错误
        }

        // 获取达人页面信息
        const cardInfo = await crawler.getAuthorCardInfo(authorId);

        console.log('📊 获取到的小红书达人详细信息:', JSON.stringify(cardInfo, null, 2));

        // 构建详细信息对象
        authorDetail = {
          platform: 'xiaohongshu',
          platformUserId: authorId,
          nickname: cardInfo.name || '未知达人',
          avatarUrl: cardInfo.headPhoto,
          followersCount: cardInfo.fansCount || 0,
          playMid: cardInfo.clickMidNum || 0,
          priceInfo: {
            post: cardInfo.picturePrice,
            video: cardInfo.videoPrice,
            live: cardInfo.lowerPrice
          },
          influencerUrl: `https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/${authorId}?source=Advertiser_Kol`,
          rawData: cardInfo,
          authorExtInfo: null // 小红书暂不支持扩展信息
        };
      } else if (platform === 'juxingtu') {
        // 调用内部方法获取巨量星图达人详细信息
        authorDetail = await InfluencerReportController.getAuthorDetailInternal(platform, authorId);
      }

      if (!authorDetail) {
        console.error('❌ authorDetail 为空，这不应该发生');
        return ResponseUtil.error(ctx, '获取达人详细信息失败，请检查达人ID是否正确', 404);
      }

      console.log('✅ 成功获取达人详细信息:', JSON.stringify(authorDetail, null, 2));

      return ResponseUtil.success(ctx, authorDetail, '获取达人详细信息成功');
    } catch (error) {
      console.error('获取达人详细信息失败:', error);

      // 特殊处理Cookie相关错误
      if (error.message.includes('没有可用的Cookie')) {
        return ResponseUtil.error(ctx, '系统暂时无法获取达人详细信息，请稍后重试或联系管理员检查Cookie配置', 503);
      }

      return ResponseUtil.error(ctx, error.message || '获取达人详细信息失败', 500);
    }
  }

  /**
   * 获取达人所有作品（用于智能提报）
   */
  static async getAuthorVideos(ctx) {
    try {
      const { platform, authorId, associateVideos = false } = ctx.request.body;

      // 验证必填字段
      if (!platform || !authorId) {
        return ResponseUtil.error(ctx, '平台和达人ID不能为空', 400);
      }

      // 验证平台类型
      const { PLATFORM_CONSTANTS } = require('../config/constants');
      if (!PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform)) {
        return ResponseUtil.error(ctx, '不支持的平台类型', 400);
      }

      // 检查是否为支持爬虫的平台
      if (!PLATFORM_CONSTANTS.CRAWLER_SUPPORTED.includes(platform)) {
        return ResponseUtil.error(ctx, '该平台暂不支持智能提报功能，请使用传统提报方式', 400);
      }

      let videosData = { notes: [], noteCount: 0 };

      if (platform === 'xiaohongshu') {
        const XiaohongshuCrawler = require('../services/crawler/crawlers/XiaohongshuCrawler');
        const crawler = new XiaohongshuCrawler();

        // 先刷新Cookie
        try {
          await crawler.refreshCookie();
        } catch (cookieError) {
          console.warn('获取Cookie失败，尝试使用默认配置:', cookieError.message);
          // 继续执行，让爬虫自己处理Cookie错误
        }

        // 获取达人所有作品
        videosData = await crawler.getAuthorAllNotes(authorId);

        console.log(`✅ 小红书达人 ${authorId} 作品数据获取成功: ${videosData.noteCount || 0} 个笔记`);

        // 无论associateVideos参数如何，都保存作品数据到数据库
        if (videosData.notes && videosData.notes.length > 0) {
          try {
            const AuthorVideoService = require('../services/AuthorVideoService');

            // 构建达人数据
            const authorData = {
              platformUserId: authorId,
              platform: platform,
              nickname: '智能提报达人' // 临时昵称，后续可以从达人信息中获取
            };

            console.log(`💾 开始保存小红书达人 ${authorId} 的 ${videosData.notes.length} 个作品到数据库...`);

            // 转换小红书笔记数据格式以适配数据库字段
            const formattedNotes = videosData.notes
              .map(note => {
                // 确保videoId字段存在且不为空
                const videoId = note.noteId || note.id || note.videoId;
                if (!videoId) {
                  console.warn('⚠️ 发现无效的笔记数据，缺少ID:', note);
                  return null;
                }

                return {
                  videoId: videoId,
                  title: note.title || note.desc || '无标题',
                  videoUrl: note.noteUrl || note.url || null,
                  videoCover: note.noteCover || note.cover || note.imageList?.[0] || null,
                  duration: 0, // 小红书笔记通常没有时长
                  publishTime: note.publishTime || note.time || null,
                  playCount: parseInt(note.readNum || note.playCount || note.viewCount || 0),
                  likeCount: parseInt(note.likeNum || note.likeCount || note.likes || 0),
                  commentCount: parseInt(note.commentNum || note.commentCount || note.comments || 0),
                  shareCount: parseInt(note.shareNum || note.shareCount || note.shares || 0),
                  collectCount: parseInt(note.collectNum || note.collectCount || note.collects || 0),
                  tags: note.tags || note.tagList || null,
                  description: note.description || note.desc || null,
                  rawData: note
                };
              })
              .filter(note => note !== null); // 过滤掉无效数据

            // 保存作品到作品库（使用不依赖达人表的方法）
            const saveResult = await AuthorVideoService.batchSaveVideosWithoutAuthorDependency(
              authorId,
              formattedNotes,
              {
                platform: platform,
                authorNickname: authorData.nickname,
                crawlTaskId: null, // 智能提报不关联爬虫任务
                forceUpdate: false
              }
            );

            console.log('✅ 小红书作品库保存结果:', {
              total: saveResult.total,
              success: saveResult.success,
              created: saveResult.created,
              updated: saveResult.updated,
              failed: saveResult.failed
            });

            // 在返回数据中添加保存结果
            videosData.saveResult = saveResult;

            // 如果associateVideos为true，则在智能提报中关联这些作品
            if (associateVideos) {
              console.log(`🔗 associateVideos=true，作品将在智能提报中关联使用`);
              videosData.associatedInReport = true;
            } else {
              console.log(`📋 associateVideos=false，作品已保存但不在智能提报中关联`);
              videosData.associatedInReport = false;
            }
          } catch (saveError) {
            console.error('❌ 保存小红书作品到作品库失败:', saveError.message);
            // 不影响主流程，只记录错误
            videosData.saveError = saveError.message;
          }
        }
      } else if (platform === 'juxingtu') {
        // 巨量星图平台 - 完整的达人作品拉取功能
        const XingtuCrawler = require('../services/crawler/crawlers/XingtuCrawler');
        const crawler = new XingtuCrawler();

        // 先刷新Cookie
        try {
          await crawler.refreshCookie();
        } catch (cookieError) {
          console.warn('获取巨量星图Cookie失败:', cookieError.message);
          throw new Error('没有可用的巨量星图Cookie，请先添加有效的Cookie后再执行查询');
        }

        console.log(`🎬 开始获取巨量星图达人 ${authorId} 的作品数据...`);

        // 获取达人视频信息
        const videoInfo = await crawler.getAuthorVideoInfo(authorId);

        if (!videoInfo || !videoInfo.videos) {
          console.warn(`⚠️ 达人 ${authorId} 没有视频数据`);
          videosData = {
            notes: [],
            noteCount: 0,
            videoStats: {
              videoCount: 0,
              averagePlay: 0,
              totalPlay: 0,
              totalLike: 0,
              totalComment: 0,
              totalShare: 0
            }
          };
        } else {
          // 转换视频数据格式为统一的notes格式
          const notes = videoInfo.videos
            .map(video => {
              // 确保videoId字段存在且不为空
              const videoId = video.videoId || video.id || video.item_id;
              if (!videoId) {
                console.warn('⚠️ 发现无效的视频数据，缺少ID:', video);
                return null;
              }

              return {
                noteId: videoId, // 保持与小红书格式一致
                videoId: videoId, // 确保数据库保存时有正确的字段
                title: video.title || '无标题',
                videoUrl: video.videoUrl || video.url || null,
                videoCover: video.videoCover || video.cover || null,
                duration: parseInt(video.duration || 0),
                publishTime: video.publishTime || video.item_date || null,
                playCount: parseInt(video.playCount || video.play || 0),
                likeCount: parseInt(video.likeCount || video.like || 0),
                commentCount: parseInt(video.commentCount || video.comment || 0),
                shareCount: parseInt(video.shareCount || video.share || 0),
                collectCount: parseInt(video.collectCount || video.collect || 0),
                platform: 'juxingtu',
                rawData: video
              };
            })
            .filter(video => video !== null); // 过滤掉无效数据

          videosData = {
            notes,
            noteCount: notes.length,
            videoStats: {
              videoCount: videoInfo.videoCount || 0,
              averagePlay: videoInfo.averagePlay || 0,
              totalPlay: videoInfo.totalPlay || 0,
              totalLike: videoInfo.totalLike || 0,
              totalComment: videoInfo.totalComment || 0,
              totalShare: videoInfo.totalShare || 0
            }
          };

          console.log(`✅ 巨量星图达人 ${authorId} 作品数据获取成功: ${notes.length} 个视频`);
        }

        // 无论associateVideos参数如何，都保存作品数据到数据库
        if (videosData.notes && videosData.notes.length > 0) {
          try {
            const AuthorVideoService = require('../services/AuthorVideoService');

            // 构建达人数据
            const authorData = {
              platformUserId: authorId,
              platform: platform,
              nickname: '智能提报达人' // 临时昵称，后续可以从达人信息中获取
            };

            console.log(`💾 开始保存巨量星图达人 ${authorId} 的 ${videosData.notes.length} 个作品到数据库...`);

            // 保存作品到作品库（使用不依赖达人表的方法）
            const saveResult = await AuthorVideoService.batchSaveVideosWithoutAuthorDependency(
              authorId,
              videosData.notes,
              {
                platform: platform,
                authorNickname: authorData.nickname,
                crawlTaskId: null, // 智能提报不关联爬虫任务
                forceUpdate: false
              }
            );

            console.log('✅ 巨量星图作品库保存结果:', {
              total: saveResult.total,
              success: saveResult.success,
              created: saveResult.created,
              updated: saveResult.updated,
              failed: saveResult.failed
            });

            // 在返回数据中添加保存结果
            videosData.saveResult = saveResult;

            // 如果associateVideos为true，则在智能提报中关联这些作品
            if (associateVideos) {
              console.log(`🔗 associateVideos=true，作品将在智能提报中关联使用`);
              videosData.associatedInReport = true;
            } else {
              console.log(`📋 associateVideos=false，作品已保存但不在智能提报中关联`);
              videosData.associatedInReport = false;
            }
          } catch (saveError) {
            console.error('❌ 保存巨量星图作品到作品库失败:', saveError.message);
            // 不影响主流程，只记录错误
            videosData.saveError = saveError.message;
          }
        }
      }

      return ResponseUtil.success(ctx, videosData, '获取作品数据成功');
    } catch (error) {
      console.error('获取作品数据失败:', error);

      // 特殊处理Cookie相关错误
      if (error.message.includes('没有可用的Cookie')) {
        return ResponseUtil.error(ctx, '系统暂时无法获取作品数据，请稍后重试或联系管理员检查Cookie配置', 503);
      }

      return ResponseUtil.error(ctx, error.message || '获取作品数据失败', 500);
    }
  }

  /**
   * 创建达人提报
   */
  static async createReport(ctx) {
    try {
      const {
        platform,
        influencerName,
        operationManager,
        influencerUrl,
        followersCount = 0,
        playMid,
        selectionReason,
        contentDirection,
        cooperationProduct,
        platformPrice,
        cooperationPrice,
        notes,
        platformUserId,
        relatedVideos // 暂时注释，等数据库字段添加后启用
      } = ctx.request.body;

      // 验证必填字段
      const requiredErrors = ValidationUtil.validateRequired(
        { platform, influencerName, operationManager, selectionReason, contentDirection },
        ['platform', 'influencerName', 'operationManager', 'selectionReason', 'contentDirection']
      );
      if (requiredErrors) {
        return ResponseUtil.validationError(ctx, requiredErrors);
      }

      // 验证平台类型
      const { PLATFORM_CONSTANTS } = require('../config/constants');
      if (!PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform)) {
        return ResponseUtil.error(ctx, '平台类型无效', 400);
      }

      // 检查30天保护期
      const protectionCheck = await InfluencerReportController.checkProtectionPeriodInternal(influencerName, platform);
      if (!protectionCheck.canSubmit) {
        return ResponseUtil.error(ctx, protectionCheck.message, 409);
      }

      // 检查重复提报控制机制
      const duplicateCheck = await InfluencerReportController.checkDuplicateReport(
        influencerName,
        platform,
        operationManager
      );
      if (!duplicateCheck.canSubmit) {
        return ResponseUtil.error(ctx, duplicateCheck.message, 409);
      }

      // 确定提报状态 - 统一使用新的状态值
      let status = 'pending';
      let resubmitCount = 0;

      if (protectionCheck.isResubmit) {
        // 查询该达人的历史提报次数
        const existingReports = await InfluencerReport.findAll({
          where: {
            influencerName,
            platform,
            submittedBy: ctx.state.user.id
          },
          order: [['createdAt', 'DESC']]
        });

        resubmitCount = existingReports.length;
      }

      console.log('准备创建提报记录，数据:', {
        platform,
        influencerName,
        operationManager,
        influencerUrl,
        followersCount: parseInt(followersCount) || 0,
        playMid,
        selectionReason,
        contentDirection,
        cooperationProduct,
        platformPrice,
        cooperationPrice,
        notes,
        status,
        submittedBy: ctx.state.user.id,
        platformUserId,
        relatedVideos,
        lastSubmittedAt: new Date()
      });

      const report = await InfluencerReport.create({
        platform,
        influencerName,
        operationManager,
        influencerUrl,
        followersCount: parseInt(followersCount) || 0,
        playMid,
        selectionReason,
        contentDirection,
        cooperationProduct,
        platformPrice,
        cooperationPrice,
        notes,
        status,
        submittedBy: ctx.state.user.id,
        platformUserId,
        relatedVideos,
        lastSubmittedAt: new Date(),
        resubmitCount
      });

      console.log('提报记录创建成功，ID:', report.id);

      // 包含关联数据返回
      const reportWithUser = await InfluencerReport.findByPk(report.id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      console.log('查询关联数据成功');
      ResponseUtil.success(ctx, reportWithUser, '创建提报成功');
    } catch (error) {
      console.error('创建提报失败详细信息:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        sql: error.sql,
        parameters: error.parameters
      });

      // 根据错误类型返回更具体的错误信息
      if (error.name === 'SequelizeValidationError') {
        const validationErrors = error.errors.map(err => `${err.path}: ${err.message}`);
        ResponseUtil.error(ctx, `数据验证失败: ${validationErrors.join(', ')}`, 400);
      } else if (error.name === 'SequelizeForeignKeyConstraintError') {
        ResponseUtil.error(ctx, '关联数据不存在，请检查用户信息', 400);
      } else if (error.name === 'SequelizeUniqueConstraintError') {
        ResponseUtil.error(ctx, '数据重复，请检查是否已存在相同提报', 400);
      } else {
        ResponseUtil.error(ctx, `创建提报失败: ${error.message}`, 500);
      }
    }
  }

  /**
   * 更新提报状态（管理员专用）
   */
  static async updateReportStatus(ctx) {
    try {
      const { id } = ctx.params;
      const { status } = ctx.request.body;

      // 验证状态值
      const validStatuses = ['submitting', 'resubmitting', 'need_communication', 'approved'];
      if (!validStatuses.includes(status)) {
        return ResponseUtil.error(ctx, '无效的状态值', 400);
      }

      const report = await InfluencerReport.findByPk(id);
      if (!report) {
        return ResponseUtil.notFound(ctx, '提报记录不存在');
      }

      await report.update({ status });

      // 返回更新后的数据
      const updatedReport = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      ResponseUtil.success(ctx, updatedReport, '更新状态成功');
    } catch (error) {
      console.error('更新提报状态失败:', error);
      ResponseUtil.error(ctx, '更新提报状态失败', 500);
    }
  }

  /**
   * 更新提报信息
   */
  static async updateReport(ctx) {
    try {
      const { id } = ctx.params;
      const {
        platform,
        influencerName,
        operationManager,
        influencerUrl,
        followersCount,
        playMid,
        selectionReason,
        platformPrice,
        cooperationPrice,
        notes
      } = ctx.request.body;

      const report = await InfluencerReport.findByPk(id);
      if (!report) {
        return ResponseUtil.notFound(ctx, '提报记录不存在');
      }

      // 检查权限：只有提报人或管理员可以编辑
      if (report.submittedBy !== ctx.state.user.id && ctx.state.user.role !== 'admin') {
        return ResponseUtil.error(ctx, '无权限编辑此提报', 403);
      }

      // 验证平台类型（如果提供了platform字段）
      if (platform !== undefined) {
        const { PLATFORM_CONSTANTS } = require('../config/constants');
        if (!PLATFORM_CONSTANTS.ALL_PLATFORMS.includes(platform)) {
          return ResponseUtil.error(ctx, '平台类型无效', 400);
        }
      }

      // 更新字段
      const updateData = {};
      if (platform !== undefined) updateData.platform = platform;
      if (influencerName !== undefined) updateData.influencerName = influencerName;
      if (operationManager !== undefined) updateData.operationManager = operationManager;
      if (influencerUrl !== undefined) updateData.influencerUrl = influencerUrl;
      if (followersCount !== undefined) updateData.followersCount = parseInt(followersCount) || 0;
      if (playMid !== undefined) updateData.playMid = playMid;
      if (selectionReason !== undefined) updateData.selectionReason = selectionReason;
      if (platformPrice !== undefined) updateData.platformPrice = platformPrice;
      if (cooperationPrice !== undefined) updateData.cooperationPrice = cooperationPrice;
      if (notes !== undefined) updateData.notes = notes;

      await report.update(updateData);

      // 返回更新后的数据
      const updatedReport = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      ResponseUtil.success(ctx, updatedReport, '更新提报成功');
    } catch (error) {
      console.error('更新提报失败:', error);
      ResponseUtil.error(ctx, '更新提报失败', 500);
    }
  }

  /**
   * 删除提报
   */
  static async deleteReport(ctx) {
    try {
      const { id } = ctx.params;

      const report = await InfluencerReport.findByPk(id);
      if (!report) {
        return ResponseUtil.notFound(ctx, '提报记录不存在');
      }

      // 检查权限：只有提报人或管理员可以删除
      if (report.submittedBy !== ctx.state.user.id && ctx.state.user.role !== 'admin') {
        return ResponseUtil.error(ctx, '无权限删除此提报', 403);
      }

      await report.destroy();

      ResponseUtil.success(ctx, null, '删除提报成功');
    } catch (error) {
      console.error('删除提报失败:', error);
      ResponseUtil.error(ctx, '删除提报失败', 500);
    }
  }

  /**
   * 获取提报详情
   */
  static async getReportById(ctx) {
    try {
      const { id } = ctx.params;

      const report = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          },
          {
            model: MyInfluencer,
            as: 'influencer',
            required: false
          },
          {
            model: PublicInfluencer,
            as: 'publicInfluencer',
            required: false
          }
        ]
      });

      if (!report) {
        return ResponseUtil.notFound(ctx, '提报记录不存在');
      }

      ResponseUtil.success(ctx, report, '获取提报详情成功');
    } catch (error) {
      console.error('获取提报详情失败:', error);
      ResponseUtil.error(ctx, '获取提报详情失败', 500);
    }
  }

  /**
   * 内部方法：检查30天保护期
   */
  static async checkProtectionPeriodInternal(influencerName, platform) {
    const existingReport = await InfluencerReport.findOne({
      where: {
        influencerName,
        platform,
        status: {
          [Op.in]: ['submitting', 'need_communication']
        }
      },
      order: [['lastSubmittedAt', 'DESC']]
    });

    if (!existingReport) {
      return {
        canSubmit: true,
        isFirstSubmit: true,
        isResubmit: false,
        message: '可以提报'
      };
    }

    const now = new Date();
    const lastSubmitted = new Date(existingReport.lastSubmittedAt);
    const daysDiff = Math.floor((now - lastSubmitted) / (1000 * 60 * 60 * 24));

    if (daysDiff < 30) {
      return {
        canSubmit: false,
        isFirstSubmit: false,
        isResubmit: false,
        daysDiff,
        remainingDays: 30 - daysDiff,
        message: `提报保护30天内，无法再次提报，需要等过保护期（还需等待${30 - daysDiff}天）`
      };
    } else {
      return {
        canSubmit: true,
        isFirstSubmit: false,
        isResubmit: true,
        daysDiff,
        message: '超过保护期，可以二次提报'
      };
    }
  }

  /**
   * 内部方法：检查CRM系统重复提报控制机制
   * @param {string} influencerName 达人名称
   * @param {Object} currentUser 当前用户信息
   * @returns {Promise<Object>} CRM检查结果
   */
  static async checkCrmDuplicateReport(influencerName, currentUser) {
    try {
      console.log(`🔍 开始CRM重复提报检查 - 达人: ${influencerName}, 用户: ${currentUser?.username}`);

      // 创建CRM服务实例
      const crmService = new CrmIntegrationService();

      // 获取当前用户的CRM用户ID
      const currentUserId = crmService.getEffectiveUserId(currentUser);
      console.log(`👤 当前用户CRM ID: ${currentUserId}`);

      // 查询CRM系统中该达人的客户记录
      const crmResult = await crmService.getCustomersByInfluencerName(influencerName, currentUserId);

      if (!crmResult.success) {
        console.warn(`⚠️ CRM查询失败: ${crmResult.message}`);
        // CRM查询失败时，允许提报但记录警告
        return {
          canSubmit: true,
          message: 'CRM系统查询失败，跳过CRM校验',
          crmQueryFailed: true,
          crmError: crmResult.message
        };
      }

      const customerRecords = crmResult.data || [];
      console.log(`📋 CRM查询结果: 找到 ${customerRecords.length} 条客户记录`);

      // 如果没有找到客户记录，允许提报
      if (customerRecords.length === 0) {
        return {
          canSubmit: true,
          message: 'CRM系统中未找到该达人的客户记录，允许提报',
          crmRecordsFound: 0
        };
      }

      // 提取所有客户记录中的负责人ID
      const chargerIds = [];
      customerRecords.forEach(record => {
        // 尝试多个可能的负责人字段名称
        const chargerId = record.charger_id || record.creator_id;
        if (chargerId && !chargerIds.includes(chargerId)) {
          chargerIds.push(chargerId);
        }
      });

      console.log(`👥 提取到的负责人ID列表: ${JSON.stringify(chargerIds)}`);

      // 如果没有找到负责人信息，允许提报
      if (chargerIds.length === 0) {
        return {
          canSubmit: true,
          message: 'CRM客户记录中未找到负责人信息，允许提报',
          crmRecordsFound: customerRecords.length,
          noChargerInfo: true
        };
      }

      // 检查当前用户ID是否在负责人列表中
      const isCurrentUserCharger = chargerIds.includes(currentUserId);

      if (isCurrentUserCharger) {
        return {
          canSubmit: true,
          message: 'CRM校验通过，当前用户是该达人的负责人，允许提报',
          crmRecordsFound: customerRecords.length,
          chargerIds: chargerIds,
          currentUserIsCharger: true
        };
      } else {
        // 获取第一个负责人ID作为冲突提报人
        const conflictChargerId = chargerIds[0];
        return {
          canSubmit: false,
          message: `该达人已在CRM系统内提报（提报人：${conflictChargerId}），你无法提报`,
          reasonCode: 'CRM_DUPLICATE_REPORT',
          crmRecordsFound: customerRecords.length,
          chargerIds: chargerIds,
          conflictChargerId: conflictChargerId,
          currentUserIsCharger: false
        };
      }
    } catch (error) {
      console.error('CRM重复提报检查失败:', error);
      // 发生异常时，允许提报但记录错误
      return {
        canSubmit: true,
        message: 'CRM校验异常，跳过CRM校验',
        crmCheckFailed: true,
        crmError: error.message
      };
    }
  }

  /**
   * 内部方法：检查重复提报控制机制
   * @param {string} influencerName 达人名称
   * @param {string} platform 平台
   * @param {string} operationManager 当前运营负责人
   * @returns {Promise<Object>} 检查结果
   */
  static async checkDuplicateReport(influencerName, platform, operationManager) {
    try {
      // 查询该达人在该平台的所有历史提报记录
      const existingReports = await InfluencerReport.findAll({
        where: {
          influencerName,
          platform
        },
        order: [['createdAt', 'DESC']], // 按创建时间排序，获取最新记录
        limit: 10 // 只查询最近10条记录，提高性能
      });

      if (existingReports.length === 0) {
        return {
          canSubmit: true,
          message: '首次提报，无重复提报限制'
        };
      }

      // 检查是否有不同运营负责人的提报记录
      const differentManagerReports = existingReports.filter(report => report.operationManager !== operationManager);

      if (differentManagerReports.length > 0) {
        // 找到最近的不同运营负责人的提报记录
        const latestDifferentReport = differentManagerReports[0];

        // 计算时间差（天数）
        const now = new Date();
        const reportCreatedAt = new Date(latestDifferentReport.createdAt);
        const timeDiff = now - reportCreatedAt;
        const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

        console.log(`重复提报检查 - 达人: ${influencerName}, 平台: ${platform}`);
        console.log(
          `最新不同运营负责人提报: ${latestDifferentReport.operationManager}, 创建时间: ${reportCreatedAt}, 天数差: ${daysDiff}, 状态: ${latestDifferentReport.status}`
        );

        // 综合判断：时间条件 AND 状态条件
        const timeConditionMet = daysDiff > 30; // 超过30天
        const statusConditionMet = latestDifferentReport.status !== 'approved'; // 状态不是审核通过

        if (timeConditionMet && statusConditionMet) {
          // 两个条件都满足，允许重复提报
          return {
            canSubmit: true,
            message: `允许重复提报：该达人的最新提报记录已超过30天且状态非审核通过`,
            allowReason: 'time_and_status_conditions_met',
            daysSinceLastReport: daysDiff,
            lastReportStatus: latestDifferentReport.status,
            lastReportManager: latestDifferentReport.operationManager
          };
        } else {
          // 条件不满足，阻止重复提报
          let reason = '';
          let reasonCode = '';

          if (!timeConditionMet && !statusConditionMet) {
            reason = `该达人已由其他运营负责人（${latestDifferentReport.operationManager}）提报，且提报时间不足30天（${daysDiff}天）并且状态为审核通过，您无权重复提报`;
            reasonCode = 'time_and_status_not_met';
          } else if (!timeConditionMet) {
            reason = `该达人已由其他运营负责人（${
              latestDifferentReport.operationManager
            }）提报，提报时间不足30天（还需等待${30 - daysDiff}天），您无权重复提报`;
            reasonCode = 'time_condition_not_met';
          } else if (!statusConditionMet) {
            reason = `该达人已由其他运营负责人（${latestDifferentReport.operationManager}）提报且状态为审核通过，您无权重复提报`;
            reasonCode = 'status_condition_not_met';
          }

          return {
            canSubmit: false,
            message: reason,
            reasonCode: reasonCode,
            conflictManager: latestDifferentReport.operationManager,
            conflictReportId: latestDifferentReport.id,
            daysSinceLastReport: daysDiff,
            lastReportStatus: latestDifferentReport.status,
            timeConditionMet: timeConditionMet,
            statusConditionMet: statusConditionMet
          };
        }
      }

      // 所有历史提报都是同一个运营负责人，允许提报
      return {
        canSubmit: true,
        message: '运营负责人验证通过，允许提报',
        existingReportsCount: existingReports.length
      };
    } catch (error) {
      console.error('检查重复提报失败:', error);
      throw error;
    }
  }

  /**
   * 获取提报详情
   */
  static async getReportById(ctx) {
    try {
      const { id } = ctx.params;

      const report = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username', 'email'],
            required: false
          }
        ]
      });

      if (!report) {
        return ResponseUtil.error(ctx, '提报记录不存在', 404);
      }

      console.log('获取提报详情成功，ID:', id);
      ResponseUtil.success(ctx, report, '获取成功');
    } catch (error) {
      console.error('获取提报详情失败:', error);
      ResponseUtil.error(ctx, '获取提报详情失败');
    }
  }

  /**
   * 审核通过
   */
  static async approveReport(ctx) {
    try {
      const { id } = ctx.params;
      const { reviewComment } = ctx.request.body;

      // 验证审核意见
      if (!reviewComment || reviewComment.trim() === '') {
        return ResponseUtil.validationError(ctx, '审核意见不能为空');
      }

      // 查找提报记录
      const report = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      if (!report) {
        return ResponseUtil.error(ctx, '提报记录不存在', 404);
      }

      // 状态检查已移除：允许任何状态的提报都可以审核通过

      // 更新状态
      await report.update({
        status: 'approved',
        reviewComment: reviewComment.trim(),
        reviewedBy: ctx.state.user.id,
        reviewedAt: new Date()
      });

      // 重新查询包含审核人信息
      const updatedReport = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      console.log('提报审核通过，ID:', id);
      ResponseUtil.success(ctx, updatedReport, '审核通过', 200, null, {
        canCreateCooperation: true,
        reportData: updatedReport
      });
    } catch (error) {
      console.error('审核通过失败:', error);
      ResponseUtil.error(ctx, '审核通过失败');
    }
  }

  /**
   * 审核拒绝
   */
  static async rejectReport(ctx) {
    try {
      const { id } = ctx.params;
      const { reviewComment } = ctx.request.body;

      // 验证审核意见
      if (!reviewComment || reviewComment.trim() === '') {
        return ResponseUtil.validationError(ctx, '审核意见不能为空');
      }

      // 查找提报记录
      const report = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      if (!report) {
        return ResponseUtil.error(ctx, '提报记录不存在', 404);
      }

      // 状态检查已移除：允许任何状态的提报都可以审核拒绝

      // 更新状态
      await report.update({
        status: 'rejected',
        reviewComment: reviewComment.trim(),
        reviewedBy: ctx.state.user.id,
        reviewedAt: new Date()
      });

      // 重新查询包含审核人信息
      const updatedReport = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      console.log('提报审核拒绝，ID:', id);
      ResponseUtil.success(ctx, updatedReport, '审核拒绝');
    } catch (error) {
      console.error('审核拒绝失败:', error);
      ResponseUtil.error(ctx, '审核拒绝失败');
    }
  }

  /**
   * 需二次确认
   */
  static async needConfirmation(ctx) {
    try {
      const { id } = ctx.params;
      const { reviewComment } = ctx.request.body;

      // 验证审核意见
      if (!reviewComment || reviewComment.trim() === '') {
        return ResponseUtil.validationError(ctx, '审核意见不能为空');
      }

      // 查找提报记录
      const report = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      if (!report) {
        return ResponseUtil.error(ctx, '提报记录不存在', 404);
      }

      // 状态检查已移除：允许任何状态的提报都可以标记为需二次确认

      // 更新状态
      await report.update({
        status: 'need_confirmation',
        reviewComment: reviewComment.trim(),
        reviewedBy: ctx.state.user.id,
        reviewedAt: new Date()
      });

      // 重新查询包含审核人信息
      const updatedReport = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      console.log('提报标记为需二次确认，ID:', id);
      ResponseUtil.success(ctx, updatedReport, '已标记为需二次确认');
    } catch (error) {
      console.error('标记需二次确认失败:', error);
      ResponseUtil.error(ctx, '标记需二次确认失败');
    }
  }

  /**
   * 重新提报
   */
  static async resubmitReport(ctx) {
    try {
      const { id } = ctx.params;
      const { resubmitReason } = ctx.request.body;

      // 验证重新提报说明
      if (!resubmitReason || resubmitReason.trim() === '') {
        return ResponseUtil.validationError(ctx, '重新提报说明不能为空');
      }

      // 查找提报记录
      const report = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      if (!report) {
        return ResponseUtil.error(ctx, '提报记录不存在', 404);
      }

      // 权限检查：任何登录用户都可以重新提报被拒绝的提报
      // 已移除原提报人限制，允许所有用户重新提报

      // 状态检查已移除：允许任何状态的提报都可以重新提报

      // 更新状态
      await report.update({
        status: 'pending',
        resubmitReason: resubmitReason.trim(),
        resubmitCount: (report.resubmitCount || 0) + 1,
        lastSubmittedAt: new Date(),
        // 清空之前的审核信息
        reviewComment: null,
        reviewedBy: null,
        reviewedAt: null
      });

      // 重新查询
      const updatedReport = await InfluencerReport.findByPk(id, {
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email']
          }
        ]
      });

      console.log('重新提报成功，ID:', id);
      ResponseUtil.success(ctx, updatedReport, '重新提报成功');
    } catch (error) {
      console.error('重新提报失败:', error);
      ResponseUtil.error(ctx, '重新提报失败');
    }
  }

  /**
   * 创建合作对接记录
   * POST /api/influencer-reports/:id/create-cooperation
   */
  static async createCooperationRecord(ctx) {
    try {
      const { id } = ctx.params;
      const userId = ctx.state.user.id;

      if (!id || isNaN(parseInt(id))) {
        return ResponseUtil.error(ctx, '无效的提报记录ID', 400);
      }

      // 获取提报记录
      const report = await InfluencerReport.findByPk(parseInt(id));
      if (!report) {
        return ResponseUtil.error(ctx, '提报记录不存在', 404);
      }

      // 检查提报状态
      if (report.status !== 'approved') {
        return ResponseUtil.error(ctx, '只有审批通过的提报才能创建合作对接记录', 400);
      }

      // 检查是否已创建合作对接记录
      if (report.cooperationRecordCreated) {
        return ResponseUtil.error(ctx, '该提报已创建过合作对接记录', 400);
      }

      // 构建合作对接记录数据
      const cooperationData = {
        // 客户信息模块
        customerName: report.influencerName,
        customerHomepage: report.influencerUrl,
        bloggerFansCount: report.followersCount?.toString() || '',
        influencerPlatformId: report.platformUserId || '',

        // 协议信息模块
        title: `${report.platform === 'xiaohongshu' ? '小红书' : '巨量星图'}-${report.influencerName}-${
          new Date().toISOString().split('T')[0]
        }`,
        publishPlatform: report.platform === 'xiaohongshu' ? '小红书' : '抖音',
        cooperationAmount: report.cooperationPrice || '',
        cooperationNotes: `来源：达人提报\n选择理由：${report.selectionReason}\n平台报价：${
          report.platformPrice || '未提供'
        }\n备注：${report.notes || '无'}`,

        // 关联字段
        influencerReportId: report.id,

        // 保留原有字段（向后兼容）
        cooperationMonth: new Date().toISOString().slice(0, 7),
        platform: report.platform,
        bloggerName: report.influencerName,
        responsiblePerson: report.operationManager,
        influencerHomepage: report.influencerUrl,
        cooperationPrice: report.cooperationPrice || '',

        // 创建人信息
        createdBy: userId,
        updatedBy: userId
      };

      // 创建合作对接记录
      const cooperation = await cooperationService.createCooperation(cooperationData, userId);

      // 更新提报记录状态
      await report.update({
        cooperationRecordId: cooperation.id,
        cooperationRecordCreated: true
      });

      ResponseUtil.success(
        ctx,
        {
          cooperationRecord: cooperation,
          influencerReport: report
        },
        '合作对接记录创建成功',
        201
      );
    } catch (error) {
      console.error('创建合作对接记录失败:', error);
      ResponseUtil.error(ctx, error.message, 400);
    }
  }

  /**
   * 检查提报是否可以创建合作对接记录
   * GET /api/influencer-reports/:id/can-create-cooperation
   */
  static async canCreateCooperationRecord(ctx) {
    try {
      const { id } = ctx.params;

      if (!id || isNaN(parseInt(id))) {
        return ResponseUtil.error(ctx, '无效的提报记录ID', 400);
      }

      const report = await InfluencerReport.findByPk(parseInt(id));
      if (!report) {
        return ResponseUtil.error(ctx, '提报记录不存在', 404);
      }

      const canCreate = report.status === 'approved' && !report.cooperationRecordCreated;

      ResponseUtil.success(
        ctx,
        {
          canCreate,
          status: report.status,
          cooperationRecordCreated: report.cooperationRecordCreated,
          cooperationRecordId: report.cooperationRecordId
        },
        '检查完成'
      );
    } catch (error) {
      console.error('检查创建权限失败:', error);
      ResponseUtil.error(ctx, error.message, 500);
    }
  }
}

module.exports = InfluencerReportController;
